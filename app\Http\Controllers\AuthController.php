<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\Pengguna;

class AuthController extends Controller
{
    /**
     * Show the login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function login(Request $request)
    {
        $request->validate([
            'login' => 'required|string',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('login', 'password');
        
        // Cari user berdasarkan login
        $user = Pengguna::where('LOGIN', $credentials['login'])->first();
        
        if ($user) {
            // Verifikasi password dengan MD5 hash
            if (md5($credentials['password']) === $user->PASSWORD) {
                // Login berhasil
                Auth::guard('pengguna')->login($user, $request->filled('remember'));

                // Set session data jika diperlukan
                session([
                    'ses_nama' => $user->NAMA ?? $user->LOGIN,
                    'ses_login' => $user->LOGIN,
                ]);

                return redirect()->intended(route('dashboard'))->with('success', 'Login berhasil!');
            }
        }
        
        // Login gagal
        return back()->withErrors([
            'login' => 'Username atau password salah.',
        ])->withInput($request->only('login'));
    }

    /**
     * Handle logout request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function logout(Request $request)
    {
        Auth::guard('pengguna')->logout();
        
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('login')->with('success', 'Logout berhasil!');
    }
}
