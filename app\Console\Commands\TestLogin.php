<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Pengguna;
use Illuminate\Support\Facades\DB;

class TestLogin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:login {login} {password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test login credentials';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $login = $this->argument('login');
        $password = $this->argument('password');
        
        try {
            // Test database connection
            $this->info('Testing database connection...');
            DB::connection()->getPdo();
            $this->info('Database connection: OK');
            
            // Test table access
            $this->info('Testing table access...');
            $user = Pengguna::where('login', $login)->first();
            
            if (!$user) {
                $this->error("User with login '{$login}' not found");
                return 1;
            }
            
            $this->info("User found: {$user->login}");
            $this->info("Stored password hash: {$user->password}");
            
            // Test password
            $inputHash = md5($password);
            $this->info("Input password hash: {$inputHash}");
            
            if ($inputHash === $user->password) {
                $this->info('Password match: YES');
                $this->info('Login test: SUCCESS');
                return 0;
            } else {
                $this->error('Password match: NO');
                $this->error('Login test: FAILED');
                return 1;
            }
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
